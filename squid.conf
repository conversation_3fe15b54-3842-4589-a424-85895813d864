# Squid configuration for container use
# Basic HTTP proxy configuration

# Port configuration
http_port 3128

# Access Control Lists (ACLs)
acl localnet src 10.0.0.0/8     # RFC1918 possible internal network
acl localnet src 172.16.0.0/12  # RFC1918 possible internal network
acl localnet src 192.168.0.0/16 # RFC1918 possible internal network
acl localnet src fc00::/7        # RFC 4193 local private network range
acl localnet src fe80::/10       # RFC 4291 link-local (directly plugged) machines

acl SSL_ports port 443
acl Safe_ports port 80          # http
acl Safe_ports port 21          # ftp
acl Safe_ports port 443         # https
acl Safe_ports port 70          # gopher
acl Safe_ports port 210         # wais
acl Safe_ports port 1025-65535  # unregistered ports
acl Safe_ports port 280         # http-mgmt
acl Safe_ports port 488         # gss-http
acl Safe_ports port 591         # filemaker
acl Safe_ports port 777         # multiling http

acl CONNECT method CONNECT

# Access rules
http_access deny !Safe_ports
http_access deny CONNECT !SSL_ports
http_access allow localhost manager
http_access deny manager
http_access allow localnet
http_access allow localhost
http_access deny all

# Cache configuration
cache_mem 256 MB
maximum_object_size_in_memory 512 KB
maximum_object_size 1 GB

# Cache directory - using ufs for better container compatibility
cache_dir ufs /var/spool/squid 1000 16 256

# Cache replacement policy
cache_replacement_policy lru

# Memory and disk cache settings
cache_swap_low 90
cache_swap_high 95

# Refresh patterns for better caching
refresh_pattern ^ftp:           1440    20%     10080
refresh_pattern ^gopher:        1440    0%      1440
refresh_pattern -i (/cgi-bin/|\?) 0     0%      0
refresh_pattern .               0       20%     4320

# Logging
access_log /var/log/squid/access.log squid
cache_log /var/log/squid/cache.log

# Process management
coredump_dir /var/spool/squid

# Network settings
dns_nameservers ******* *******

# Disable via header for privacy
via off
forwarded_for off

# Error page customization
error_directory /usr/share/squid/errors/en